"""
Validation script for MAT2 v6 model - checks algorithm structure and field mappings
"""

import os
import csv
import sys


def validate_csv_structure():
    """Validate the structure of input CSV files"""
    print("🔍 Validating CSV file structures...")

    # Check jordstykke.csv
    jordstykke_path = "jordstykke.csv"
    if os.path.exists(jordstykke_path):
        with open(jordstykke_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            jordstykke_fields = reader.fieldnames
            print(f"📋 Jordstykke fields: {jordstykke_fields}")

            # Check required fields
            required_fields = ["regionskode", "samletfastejendomlokalid", "lokalid"]
            missing = [
                field for field in required_fields if field not in jordstykke_fields
            ]
            if missing:
                print(f"❌ Missing required fields in jordstykke: {missing}")
            else:
                print("✅ All required fields present in jordstykke")
    else:
        print(f"❌ jordstykke.csv not found")

    # Check stamdata CSV
    stamdata_path = "stam_avjnf.csv"
    if os.path.exists(stamdata_path):
        with open(stamdata_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            stamdata_fields = reader.fieldnames
            print(f"📋 Stamdata fields: {stamdata_fields}")

            if "BFE-nummer" in stamdata_fields:
                print("✅ BFE-nummer field found in stamdata")
            else:
                print("❌ BFE-nummer field missing in stamdata")
    else:
        print(f"❌ stam_avjnf.csv not found")

    # Check jordstykker CSV
    jordstykker_path = "jord_avjnf.csv"
    if os.path.exists(jordstykker_path):
        with open(jordstykker_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            jordstykker_fields = reader.fieldnames
            print(f"📋 Jordstykker fields: {jordstykker_fields}")

            if "JordstykkeID" in jordstykker_fields:
                print("✅ JordstykkeID field found in jordstykker")
            else:
                print("❌ JordstykkeID field missing in jordstykker")
    else:
        print(f"❌ jord_avjnf.csv not found")


def validate_algorithm_structure():
    """Validate the MAT2 v6 algorithm structure"""
    print("\n🔍 Validating MAT2 v6 algorithm structure...")

    try:
        # Import the algorithm
        sys.path.append(os.getcwd())
        from RS_join_model_MAT2_Regioner_v6 import RSJoinModelMAT2RegionerV6

        # Create instance
        algorithm = RSJoinModelMAT2RegionerV6()

        # Check key methods exist
        required_methods = [
            "_process_with_regional_split",
            "_process_single_layer",
            "_join_with_stamdata",
            "_join_with_jordstykker",
            "_refactor_fields_v6",
            "_filter_null_jordstykke_id",
            "_get_field_mappings_v6",
        ]

        for method in required_methods:
            if hasattr(algorithm, method):
                print(f"✅ Method {method} found")
            else:
                print(f"❌ Method {method} missing")

        # Check field mappings
        print("\n🔍 Validating field mappings...")
        try:
            # Create a mock layer object for testing
            class MockLayer:
                pass

            mappings = algorithm._get_field_mappings_v6(MockLayer())
            print(f"📊 Total field mappings: {len(mappings)}")

            # Check key field mappings
            field_names = [mapping["name"] for mapping in mappings]
            key_fields = [
                "PropertyID",
                "LocalID",
                "JordstykkeID",
                "BFE_nummer",
                "Grundareal_m2",
                "Grundareal_ha",
                "Ejendomsvaerdi_kr",
            ]

            for field in key_fields:
                if field in field_names:
                    print(f"✅ Key field {field} mapped")
                else:
                    print(f"❌ Key field {field} missing from mappings")

            # Check data types
            type_counts = {}
            for mapping in mappings:
                field_type = mapping["type"]
                type_counts[field_type] = type_counts.get(field_type, 0) + 1

            print(f"📊 Field type distribution: {type_counts}")
            print("   Type 10 = String, Type 6 = Real/Double, Type 2 = Integer")

        except Exception as e:
            print(f"❌ Error testing field mappings: {e}")

    except ImportError as e:
        print(f"❌ Failed to import algorithm: {e}")
    except Exception as e:
        print(f"❌ Error validating algorithm: {e}")


def check_v6_improvements():
    """Check specific v6 improvements"""
    print("\n🔍 Checking v6 improvements...")

    try:
        with open("RS_join_model_MAT2_Regioner_v6.py", "r", encoding="utf-8") as f:
            content = f.read()

        # Check for regionskode usage restriction
        regionskode_usages = content.count('"regionskode"')
        print(f"📊 'regionskode' field references: {regionskode_usages}")

        if "ONLY use regionskode for splitting" in content:
            print("✅ Regionskode usage restriction documented")
        else:
            print("❌ Regionskode usage restriction not clearly documented")

        # Check for best practice data handling
        if "best practice data handling" in content.lower():
            print("✅ Best practice data handling mentioned")
        else:
            print("❌ Best practice data handling not mentioned")

        # Check for proper numeric conversion
        if "to_real(" in content and "regexp_replace(" in content:
            print("✅ Proper numeric conversion functions used")
        else:
            print("❌ Numeric conversion may need improvement")

        # Check for QGIS compatibility focus
        if "QGIS compatibility" in content:
            print("✅ QGIS compatibility mentioned")
        else:
            print("❌ QGIS compatibility not explicitly mentioned")

    except Exception as e:
        print(f"❌ Error checking v6 improvements: {e}")


def main():
    """Main validation function"""
    print("🚀 MAT2 v6 Validation Report")
    print("=" * 50)

    validate_csv_structure()
    validate_algorithm_structure()
    check_v6_improvements()

    print("\n" + "=" * 50)
    print("✅ Validation completed!")


if __name__ == "__main__":
    main()
