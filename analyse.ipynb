import pandas as pd
import numpy as np  # noqa: F401


jord = pd.read_csv('Jordstykker_RS.csv')
stam = pd.read_csv('Stamdata_RS.csv')
ejendomme = pd.read_excel('250604_arealoversigt_Resights_2026_RawData.xlsx', sheet_name='<PERSON>rdstykker')

print('Jordstykker:', jord.shape)
print('Stamdata:', stam.shape)  
print('Ejendomme:', ejendomme.shape)

pd.set_option('display.max_columns', None)
print('Jordstykker_RS.csv info:\n')
print(jord.info())
print(jord.head())
print(jord.describe())

print('\nStamdata_RS.csv info:\n')
print(stam.info())
print(stam.head())
print(stam.describe())

stam_ejer_dist = [stam["Primær ejer"].value_counts(),
                 stam["Primær ejer"].unique().tolist()
                ]
print(f'\nPrimær ejer distribution:\n{stam_ejer_dist}')

stam_avjnf = stam[
    (stam['Primær ejer'] == 'AAGE V. JENSEN NATURFOND') |
    (stam['Primær ejer'] == 'Lille Vildmose Naturfond')
]
print('\nAAGE V. JENSEN NATURFOND info:')
print(stam_avjnf.info())
print('\n')
print(stam_avjnf.head())

# Get unique values from JordstykkeID in jord
jordstykke_ids = set(jord["JordstykkeID"].dropna().astype(str))
print(f"Unique JordstykkeID values in jord: {len(jordstykke_ids)}")

# Check each column in stam_avjnf (filtered data) for matches with JordstykkeID
potential_foreign_keys = {}

for col in stam_avjnf.columns:
    try:
        stam_values = set(stam_avjnf[col].dropna().astype(str))
        overlap = jordstykke_ids & stam_values

        if len(overlap) > 0:
            ratio = len(overlap) / len(jordstykke_ids)
            potential_foreign_keys[col] = {
                "overlap": len(overlap),
                "ratio": ratio,
                "stam_unique_count": len(stam_values),
            }
    except (TypeError, ValueError, AttributeError):
        continue

# Display results sorted by overlap ratio
print("\nPotential foreign keys in stam_avjnf matching JordstykkeID:")
print(f"Total columns checked: {len(stam_avjnf.columns)}")
print(f"Columns with matches: {len(potential_foreign_keys)}")

if potential_foreign_keys:
    for col, stats in sorted(
        potential_foreign_keys.items(), key=lambda x: x[1]["ratio"], reverse=True
    ):
        print(f"\nColumn '{col}':")
        print(f"  - Matching values: {stats['overlap']}")
        print(f"  - Match ratio: {stats['ratio']:.2%}")
        print(f"  - Unique values in stam column: {stats['stam_unique_count']}")
else:
    print("No matching columns found!")
    print("\nFirst few JordstykkeID values:")
    print(list(jordstykke_ids)[:10])
    print("\nSample of stam_avjnf column names:")
    print(stam_avjnf.columns.tolist()[:10])

for col in stam_avjnf.columns:
    try:
        stam_values = set(stam_avjnf[col].dropna().astype(str))
        overlap = jordstykke_ids & stam_values
        
        if len(overlap) > 0:
            ratio = len(overlap) / len(jordstykke_ids)
            potential_foreign_keys[col] = {
                "overlap": len(overlap),
                "ratio": ratio,
                "stam_unique_count": len(stam_values),
            }
    except (TypeError, ValueError, AttributeError):
        # Skip columns that can't be converted to string or processed
        continue

# Filter jord to only include BFE-nummer values that exist in stam_avjnf
jord_avjnf = jord[jord["BFE-nummer"].isin(stam_avjnf["BFE-nummer"])]

print("\nFiltered Jordstykker for AAGE V. JENSEN NATURFOND:")
print(f"Original jord shape: {jord.shape}")
print(f"Filtered jord_avjnf shape: {jord_avjnf.shape}")
print(
    f'Number of unique BFE-nummer in stam_avjnf: {stam_avjnf["BFE-nummer"].nunique()}'
)
print(
    f'Number of unique BFE-nummer in jord_avjnf: {jord_avjnf["BFE-nummer"].nunique()}'
)

print("\nFirst few rows of filtered jordstykker:")
print(jord_avjnf.head())

# Verify the filtering worked correctly
print("\nUnique BFE-nummer values in filtered data:")
print(sorted(jord_avjnf["BFE-nummer"].unique()))

# Find BFE-nummer values that exist in stam_avjnf but not in jord_avjnf
missing_in_jord = set(stam_avjnf["BFE-nummer"]) - set(jord_avjnf["BFE-nummer"])

print("\nBFE-nummer values in stam_avjnf but NOT in jord:")
print(f"Count: {len(missing_in_jord)}")
print(f"Values: {sorted(missing_in_jord)}")

# Show the properties that don't have land parcels
if missing_in_jord:
    print("\nProperties without land parcels:")
    missing_properties = stam_avjnf[stam_avjnf["BFE-nummer"].isin(missing_in_jord)]
    print(
        missing_properties[["BFE-nummer", "Adresse", "Type", "Anvendelse"]].to_string()
    )

# Also check the reverse - any BFE in jord but not in stam_avjnf (should be 0)
missing_in_stam = set(jord_avjnf["BFE-nummer"]) - set(stam_avjnf["BFE-nummer"])
print(f"\nBFE-nummer in jord_avjnf but NOT in stam_avjnf: {len(missing_in_stam)}")