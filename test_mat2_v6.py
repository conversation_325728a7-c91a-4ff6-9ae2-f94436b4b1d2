"""
Test script for MAT2 v6 model with sample data
"""

import os
import sys
from qgis.core import (
    QgsApplication, 
    QgsVectorLayer, 
    QgsProcessingContext,
    QgsProcessingFeedback,
    QgsProject
)
import processing

# Initialize QGIS application
def init_qgis():
    """Initialize QGIS application for testing"""
    QgsApplication.setPrefixPath(r"C:\Program Files\QGIS 3.34.0", True)
    qgs = QgsApplication([], False)
    qgs.initQgis()
    
    # Add processing provider
    from qgis.analysis import QgsNativeAlgorithms
    QgsApplication.processingRegistry().addProvider(QgsNativeAlgorithms())
    
    return qgs

def test_mat2_v6():
    """Test the MAT2 v6 model with sample data"""
    print("🚀 Testing MAT2 v6 model...")
    
    # Initialize QGIS
    qgs = init_qgis()
    
    try:
        # Import the v6 algorithm
        sys.path.append(os.getcwd())
        from RS_join_model_MAT2_Regioner_v6 import RSJoinModelMAT2RegionerV6
        
        # Setup paths
        base_dir = os.getcwd()
        jordstykke_path = os.path.join(base_dir, 'jordstykke.csv')
        stamdata_path = os.path.join(base_dir, 'stam_avjnf.csv')
        jordstykker_path = os.path.join(base_dir, 'jord_avjnf.csv')
        output_path = os.path.join(base_dir, 'test_output_v6.gpkg')
        
        # Check if input files exist
        for path, name in [(jordstykke_path, 'jordstykke'), (stamdata_path, 'stamdata'), (jordstykker_path, 'jordstykker')]:
            if not os.path.exists(path):
                print(f"❌ Missing input file: {name} at {path}")
                return False
        
        print("✅ All input files found")
        
        # Load jordstykke CSV as vector layer
        print("📂 Loading jordstykke CSV...")
        jordstykke_layer = QgsVectorLayer(f"file:///{jordstykke_path}?delimiter=,", "jordstykke", "delimitedtext")
        
        if not jordstykke_layer.isValid():
            print("❌ Failed to load jordstykke CSV")
            return False
            
        print(f"✅ Jordstykke layer loaded: {jordstykke_layer.featureCount()} features")
        print(f"📋 Fields: {[field.name() for field in jordstykke_layer.fields()]}")
        
        # Check required fields
        required_fields = ['regioner', 'samletfastejendomlokalid', 'lokalid']
        layer_fields = [field.name() for field in jordstykke_layer.fields()]
        missing_fields = [field for field in required_fields if field not in layer_fields]
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
            
        print("✅ All required fields present")
        
        # Create algorithm instance
        algorithm = RSJoinModelMAT2RegionerV6()
        
        # Setup parameters
        parameters = {
            algorithm.INPUT_JORDSTYKKE: jordstykke_layer,
            algorithm.INPUT_STAMDATA: stamdata_path,
            algorithm.INPUT_JORDSTYKKER: jordstykker_path,
            algorithm.OUTPUT_GPKG: output_path,
            algorithm.REGIONS_TO_PROCESS: '',  # Process all regions
            algorithm.USE_REGIONAL_SPLIT: False  # Test without regional split first
        }
        
        # Setup context and feedback
        context = QgsProcessingContext()
        feedback = QgsProcessingFeedback()
        
        # Run the algorithm
        print("🔄 Running MAT2 v6 algorithm...")
        result = algorithm.processAlgorithm(parameters, context, feedback)
        
        if result and os.path.exists(output_path):
            print(f"✅ Algorithm completed successfully!")
            print(f"📁 Output saved to: {output_path}")
            
            # Load and inspect output
            output_layer = QgsVectorLayer(output_path, "test_output", "ogr")
            if output_layer.isValid():
                print(f"📊 Output features: {output_layer.featureCount()}")
                print(f"📋 Output fields: {[field.name() for field in output_layer.fields()]}")
                
                # Show sample feature
                if output_layer.featureCount() > 0:
                    feature = next(output_layer.getFeatures())
                    print("📝 Sample feature attributes:")
                    for field in output_layer.fields():
                        value = feature[field.name()]
                        print(f"   {field.name()}: {value}")
                        
                return True
            else:
                print("❌ Failed to load output layer")
                return False
        else:
            print("❌ Algorithm failed or no output produced")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        qgs.exitQgis()

if __name__ == "__main__":
    success = test_mat2_v6()
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("💥 Test failed!")
        sys.exit(1)
