"""
RS Join Model MAT2 Regioner v7 - Enhanced Hybrid Version
Combines the robust structure from v5 with the corrected data handling from v6.

This model:
1. Splits jordstykke base layer by regions for efficient processing ONLY (using 'regionskode')
2. Joins samletfastejendomlokalid with BFE-nummer from stamdata CSV
3. Joins lokalid with <PERSON><PERSON>tykkeID from jordstykker CSV
4. Refactors fields with best practice data handling and proper Danish formatting
5. Filters out records where <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> IS NULL
6. Outputs enhanced GeoPackage with all property attributes

Key improvements in v7 (hybrid approach):
- PRESERVED: Robust structure and processability from v5
- INCORPORATED: Fixed 'regionskode' usage (only for base layer splitting) from v6
- INCORPORATED: Best practice data handling for QGIS compatibility from v6
- PRESERVED: Comprehensive field mappings and data cleaning from v5
- PRESERVED: Enhanced error handling and temp file management from v5
- IMPROVED: Optimized field mappings combining both approaches
- IMPROVED: Better coordinate and area formatting with proper Danish support

Author: AI Assistant
Version: 7.0 (Hybrid v5 + v6)
Date: 2024
"""

import os
import tempfile
import shutil
from datetime import datetime
from qgis.core import (
    QgsProcessing,
    QgsProcessingAlgorithm,
    QgsProcessingParameterVectorLayer,
    QgsProcessingParameterFile,
    QgsProcessingParameterFileDestination,
    QgsProcessingParameterString,
    QgsProcessingParameterBoolean,
    QgsProcessingUtils,
    QgsVectorLayer,
    QgsMessageLog,
    Qgis,
)
from qgis import processing


class RSJoinModelMAT2RegionerV7(QgsProcessingAlgorithm):
    """
    Enhanced MAT2 workflow with regional processing and best practice data handling.

    Hybrid v7 approach combining:
    - Robust structure and processability from v5
    - Fixed 'regionskode' usage (only for base layer splitting) from v6
    - Best practice data handling for QGIS compatibility from v6
    - Comprehensive field mappings and data cleaning from v5
    """

    # Parameter names
    INPUT_JORDSTYKKE = "INPUT_JORDSTYKKE"
    INPUT_STAMDATA = "INPUT_STAMDATA"
    INPUT_JORDSTYKKER = "INPUT_JORDSTYKKER"
    OUTPUT_GPKG = "OUTPUT_GPKG"
    REGIONS_TO_PROCESS = "REGIONS_TO_PROCESS"
    USE_REGIONAL_SPLIT = "USE_REGIONAL_SPLIT"
    TEMP_DIRECTORY = "TEMP_DIRECTORY"

    # Field mappings as class constants for maintainability (from v5)
    JORDSTYKKER_FIELD_MAP = [
        {
            "expression": '"JordstykkeID"',
            "length": 0,
            "name": "JordstykkeID",
            "precision": 0,
            "type": 2,  # QVariant.Int
        },
        {
            "expression": '"Matrikelnr"',
            "length": 0,
            "name": "Matrikelnr",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Ejerlavsnavn"',
            "length": 0,
            "name": "Ejerlavsnavn",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        # Land-related attributes from Jordstykker_RS
        {
            "expression": '"Vejareal"',
            "length": 0,
            "name": "Vejareal",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Fredskovsareal"',
            "length": 0,
            "name": "Fredskovsareal",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Strandbeskyttelsesareal"',
            "length": 0,
            "name": "Strandbeskyttelsesareal",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Fredskovsareal omfang"',
            "length": 0,
            "name": "Fredskovsareal omfang",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Strandbeskyttelse omfang"',
            "length": 0,
            "name": "Strandbeskyttelse omfang",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Er fælleslod"',
            "length": 0,
            "name": "Er fælleslod",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Longitude"',
            "length": 0,
            "name": "Longitude",
            "precision": 6,
            "type": 6,  # QVariant.Double
        },
        {
            "expression": '"Latitude"',
            "length": 0,
            "name": "Latitude",
            "precision": 6,
            "type": 6,  # QVariant.Double
        },
    ]

    STAMDATA_FIELD_MAP = [
        # Original ownership fields
        {
            "expression": '"BFE-nummer"',
            "length": 0,
            "name": "BFE-nummer",
            "precision": 0,
            "type": 2,  # QVariant.Int
        },
        {
            "expression": '"Antal ejere"',
            "length": 0,
            "name": "Antal ejere",
            "precision": 0,
            "type": 2,  # QVariant.Int
        },
        {
            "expression": '"Primær ejer"',
            "length": 0,
            "name": "Primær ejer",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Primær ejer alder"',
            "length": 0,
            "name": "Primær ejer alder",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Postlinje 1"',
            "length": 0,
            "name": "Postlinje 1",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Postnr"',
            "length": 0,
            "name": "Postnr",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"By"',
            "length": 0,
            "name": "By",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        # Building/property attributes
        {
            "expression": '"Seneste handelspris"',
            "length": 0,
            "name": "Seneste handelspris",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Seneste handelsdato"',
            "length": 0,
            "name": "Seneste handelsdato",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Anvendelse"',
            "length": 0,
            "name": "Anvendelse",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Opførelsesår"',
            "length": 0,
            "name": "Opførelsesår",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Type"',
            "length": 0,
            "name": "Type",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Undertype"',
            "length": 0,
            "name": "Undertype",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Energimærke"',
            "length": 0,
            "name": "Energimærke",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Offentlig ejendomsværdi (Ny)"',
            "length": 0,
            "name": "Offentlig ejendomsværdi (Ny)",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        {
            "expression": '"Realiseret bebyggelsesprocent"',
            "length": 0,
            "name": "Realiseret bebyggelsesprocent",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
        # Land-related field from Stamdata_RS
        {
            "expression": '"Grundareal"',
            "length": 0,
            "name": "Grundareal",
            "precision": 0,
            "type": 10,  # QVariant.String
        },
    ]

    def __init__(self):
        super().__init__()
        self.temp_dir = None
        self.temp_files = []
        self.temp_layers = []  # From v5 for robust temp management
        self.context = None
        self.feedback = None

    def tr(self, string):
        return string

    def createInstance(self):
        return RSJoinModelMAT2RegionerV7()

    def name(self):
        return "rs_join_model_mat2_regioner_v7"

    def displayName(self):
        return "RS Join Model MAT2 Regioner v7 (Hybrid)"

    def group(self):
        return "MAT2 Processing"

    def groupId(self):
        return "mat2_processing"

    def shortHelpString(self):
        return """
        Enhanced MAT2 workflow v7 - Hybrid approach combining v5 robustness with v6 improvements.
        
        This algorithm processes jordstykke data with regional splitting and joins it with:
        - Stamdata CSV with 'BFE-nummer' field  
        - Jordstykker CSV with 'JordstykkeID' field
        """

    def initAlgorithm(self, config=None):
        # Input parameters
        self.addParameter(
            QgsProcessingParameterVectorLayer(
                self.INPUT_JORDSTYKKE,
                "Jordstykke Layer",
                [QgsProcessing.TypeVectorPolygon],
            )
        )

        self.addParameter(
            QgsProcessingParameterFile(
                self.INPUT_STAMDATA,
                "Stamdata CSV File",
                behavior=QgsProcessingParameterFile.File,
                fileFilter="CSV files (*.csv)",
            )
        )

        self.addParameter(
            QgsProcessingParameterFile(
                self.INPUT_JORDSTYKKER,
                "Jordstykker CSV File",
                behavior=QgsProcessingParameterFile.File,
                fileFilter="CSV files (*.csv)",
            )
        )

        self.addParameter(
            QgsProcessingParameterFileDestination(
                self.OUTPUT_GPKG, "Output GeoPackage", "GeoPackage files (*.gpkg)"
            )
        )

        self.addParameter(
            QgsProcessingParameterString(
                self.REGIONS_TO_PROCESS,
                "Regions to Process (comma-separated, leave empty for all)",
                "",
                optional=True,
            )
        )

        self.addParameter(
            QgsProcessingParameterBoolean(
                self.USE_REGIONAL_SPLIT,
                "Use Regional Split Processing",
                True,
            )
        )

        # Enhanced temp management
        self.addParameter(
            QgsProcessingParameterFile(
                self.TEMP_DIRECTORY,
                "Temporary Directory",
                behavior=QgsProcessingParameterFile.Folder,
                optional=True,
                defaultValue=None,
            )
        )

    def processAlgorithm(self, parameters, context, feedback):
        """Main processing algorithm"""
        try:
            # Setup
            self._setup_processing(parameters, context, feedback)

            # Get input parameters
            jordstykke_layer = self.parameterAsVectorLayer(
                parameters, self.INPUT_JORDSTYKKE, context
            )
            stamdata_path = self.parameterAsFile(
                parameters, self.INPUT_STAMDATA, context
            )
            jordstykker_path = self.parameterAsFile(
                parameters, self.INPUT_JORDSTYKKER, context
            )
            output_path = self.parameterAsFileOutput(
                parameters, self.OUTPUT_GPKG, context
            )
            regions_str = self.parameterAsString(
                parameters, self.REGIONS_TO_PROCESS, context
            )
            use_regional_split = self.parameterAsBool(
                parameters, self.USE_REGIONAL_SPLIT, context
            )

            # Validate inputs
            self._validate_inputs(
                jordstykke_layer, stamdata_path, jordstykker_path, feedback
            )

            # Process based on regional split setting
            if use_regional_split:
                result_layer = self._process_with_regional_split(
                    jordstykke_layer,
                    stamdata_path,
                    jordstykker_path,
                    regions_str,
                    context,
                    feedback,
                )
            else:
                result_layer = self._process_single_layer(
                    jordstykke_layer, stamdata_path, jordstykker_path, context, feedback
                )

            # Save final output
            self._save_final_output(result_layer, output_path, context, feedback)

            feedback.pushInfo(f"✅ Processing completed successfully!")
            feedback.pushInfo(f"📁 Output saved to: {output_path}")

            return {self.OUTPUT_GPKG: output_path}

        except Exception as e:
            feedback.reportError(f"❌ Processing failed: {str(e)}")
            raise
        finally:
            self._cleanup_temp_files()

    def _setup_processing(self, parameters, context, feedback):
        """Setup processing environment with enhanced temp management"""
        self.context = context
        self.feedback = feedback

        # Get temp directory from parameters or use system default
        temp_base = self.parameterAsFile(parameters, self.TEMP_DIRECTORY, context)

        # If user specified a temp directory, use it
        if temp_base and os.path.exists(temp_base):
            if not os.access(temp_base, os.W_OK):
                feedback.pushInfo(
                    f"⚠️ No write access to {temp_base}, using system temp"
                )
                temp_base = None

        # Create session directory if we have a base directory
        if temp_base:
            # Create unique session directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.temp_dir = os.path.join(temp_base, f"mat2_v7_processing_{timestamp}")

            try:
                os.makedirs(self.temp_dir, exist_ok=True)
                feedback.pushInfo(f"✅ Custom temp directory: {self.temp_dir}")
            except Exception as e:
                feedback.pushInfo(f"⚠️ Cannot create temp directory: {str(e)}")
                feedback.pushInfo("   Using system temp instead")
                self.temp_dir = tempfile.mkdtemp(prefix="mat2_v7_")
        else:
            # No specific temp directory, use system temp
            self.temp_dir = tempfile.mkdtemp(prefix="mat2_v7_")
            feedback.pushInfo(f"✅ System temp directory: {self.temp_dir}")

        self.temp_files = []
        self.temp_layers = []

        feedback.pushInfo("🚀 Starting MAT2 v7 Hybrid Processing")

    def _validate_inputs(
        self, jordstykke_layer, stamdata_path, jordstykker_path, feedback
    ):
        """Validate input data"""
        feedback.pushInfo("🔍 Validating inputs...")

        # Check jordstykke layer fields
        required_fields = ["regionskode", "samletfastejendomlokalid", "lokalid"]
        layer_fields = [field.name() for field in jordstykke_layer.fields()]

        missing_fields = [
            field for field in required_fields if field not in layer_fields
        ]
        if missing_fields:
            raise ValueError(
                f"Missing required fields in jordstykke layer: {missing_fields}"
            )

        # Check CSV files exist
        if not os.path.exists(stamdata_path):
            raise FileNotFoundError(f"Stamdata CSV not found: {stamdata_path}")
        if not os.path.exists(jordstykker_path):
            raise FileNotFoundError(f"Jordstykker CSV not found: {jordstykker_path}")

        feedback.pushInfo("✅ Input validation passed")

    def _process_with_regional_split(
        self,
        jordstykke_layer,
        stamdata_path,
        jordstykker_path,
        regions_str,
        context,
        feedback,
    ):
        """Process with regional splitting for efficiency"""
        feedback.pushInfo("🌍 Processing with regional split...")

        # Parse regions
        if regions_str.strip():
            regions = [r.strip() for r in regions_str.split(",") if r.strip()]
            feedback.pushInfo(f"📍 Processing specific regions: {regions}")
        else:
            # Get all unique regions from the layer
            regions = self._get_unique_regions(jordstykke_layer, feedback)
            feedback.pushInfo(f"📍 Processing all regions: {regions}")

        processed_layers = []

        for i, region in enumerate(regions):
            feedback.pushInfo(f"🔄 Processing region {region} ({i+1}/{len(regions)})")

            # Filter by region - ONLY use 'regionskode' field for this split
            region_layer = self._filter_by_region(
                jordstykke_layer, region, context, feedback
            )

            if region_layer.featureCount() == 0:
                feedback.pushInfo(f"⚠️ No features found for region {region}, skipping")
                continue

            # Process this region
            processed_layer = self._process_single_layer(
                region_layer, stamdata_path, jordstykker_path, context, feedback
            )

            if processed_layer and processed_layer.featureCount() > 0:
                processed_layers.append(processed_layer)
                feedback.pushInfo(
                    f"✅ Region {region} processed: {processed_layer.featureCount()} features"
                )
            else:
                feedback.pushInfo(f"⚠️ No valid features for region {region}")

        if not processed_layers:
            raise ValueError("No valid features found in any region")

        # Merge all regional results
        feedback.pushInfo("🔗 Merging regional results...")
        return self._merge_layers(processed_layers, context, feedback)

    def _get_unique_regions(self, layer, feedback):
        """Get unique values from regionskode field"""
        regions = set()
        for feature in layer.getFeatures():
            region = feature["regionskode"]
            if region is not None:
                regions.add(str(region))

        return sorted(list(regions))

    def _filter_by_region(self, layer, region, context, feedback):
        """Filter layer by region - ONLY use regionskode field for splitting"""
        temp_path = os.path.join(self.temp_dir, f"region_{region}.gpkg")
        self.temp_files.append(temp_path)

        # Use native:extractbyattribute to filter by regionskode field
        result = processing.run(
            "native:extractbyattribute",
            {
                "INPUT": layer,
                "FIELD": "regionskode",  # ONLY use regionskode for splitting
                "OPERATOR": 0,  # =
                "VALUE": region,
                "OUTPUT": temp_path,
            },
            context=context,
            feedback=feedback,
        )

        return QgsVectorLayer(result["OUTPUT"], f"region_{region}", "ogr")

    def _process_single_layer(
        self, layer, stamdata_path, jordstykker_path, context, feedback
    ):
        """Process a single layer through the MAT2 workflow with v5 robustness"""
        feedback.pushInfo(f"🔄 Processing layer with {layer.featureCount()} features")

        # Load CSV files as layers with proper cleaning (from v5)
        feedback.pushInfo("📁 Loading and cleaning CSV data files...")
        stamdata_layer = self._load_csv_as_layer(stamdata_path, "stamdata_rs", feedback)
        jordstykker_layer = self._load_csv_as_layer(
            jordstykker_path, "jordstykker_rs", feedback
        )

        # Step 0: Clean numeric data in CSV layers FIRST (from v5)
        feedback.pushInfo("🧹 Step 0: Cleaning stamdata numeric formats...")
        stamdata_cleaned = self._clean_numeric_data(stamdata_layer, feedback)
        if not stamdata_cleaned:
            return None

        feedback.pushInfo("🧹 Step 0: Cleaning jordstykker numeric formats...")
        jordstykker_cleaned = self._clean_numeric_data(jordstykker_layer, feedback)
        if not jordstykker_cleaned:
            return None

        # Step 1: Refactor Jordstykker fields (with cleaned data)
        feedback.pushInfo("📊 Step 1: Refactoring Jordstykker fields...")
        jordstykker_refactored = self._refactor_fields(
            jordstykker_cleaned,
            self.JORDSTYKKER_FIELD_MAP,
            "Refactoring Jordstykker fields",
            feedback,
        )
        if not jordstykker_refactored:
            return None

        # Step 2: Convert text to integer
        feedback.pushInfo("🔧 Step 2: Converting text fields to integer...")
        region_converted = self._convert_text_to_integer(layer, feedback)
        if not region_converted:
            return None

        # Step 3: Refactor Stamdata fields (with cleaned data)
        feedback.pushInfo("📊 Step 3: Refactoring Stamdata fields...")
        stamdata_refactored = self._refactor_fields(
            stamdata_cleaned,
            self.STAMDATA_FIELD_MAP,
            "Refactoring Stamdata fields",
            feedback,
        )
        if not stamdata_refactored:
            return None

        # Step 4: Join region with stamdata
        feedback.pushInfo("🔗 Step 4: Joining with stamdata...")
        join1_result = self._perform_join(
            region_converted,
            stamdata_refactored,
            "samletfastejendomlokalid",
            "BFE-nummer",
            "Joining with stamdata",
            feedback,
        )
        if not join1_result:
            return None

        # Step 5: Join with jordstykker
        feedback.pushInfo("🔗 Step 5: Joining with jordstykker...")
        join2_result = self._perform_join(
            join1_result,
            jordstykker_refactored,
            "lokalid",
            "JordstykkeID",
            "Joining with jordstykker",
            feedback,
            one_to_many=True,
        )
        if not join2_result:
            return None

        # Step 6: Final field setup with v7 hybrid approach
        feedback.pushInfo("🔧 Step 6: Setting up final fields...")
        final_fields = self._setup_final_fields_v7(join2_result, feedback)
        if not final_fields:
            return None

        # Step 7: Extract non-null features
        feedback.pushInfo("🔍 Step 7: Filtering NULL JordstykkeID...")
        filtered = self._filter_null_jordstykke_id(final_fields, context, feedback)

        feedback.pushInfo(
            f"✅ Layer processed: {filtered.featureCount()} valid features"
        )
        return filtered

    def _load_csv_as_layer(self, csv_path, layer_name, feedback):
        """Load CSV file as vector layer with proper field detection (from v5)"""
        feedback.pushInfo(f"Loading CSV: {os.path.basename(csv_path)}")

        # Normalize path for Windows
        csv_path = os.path.normpath(csv_path).replace("\\", "/")

        # Build URI for CSV layer with proper options
        uri = (
            f"file:///{csv_path}?"
            f"type=csv&"
            f"delimiter=,&"
            f"detectTypes=yes&"
            f"geomType=none&"
            f"subsetIndex=no&"
            f"watchFile=no&"
            f"encoding=UTF-8"
        )

        # Create layer
        layer = QgsVectorLayer(uri, layer_name, "delimitedtext")

        if not layer.isValid() or layer.featureCount() == 0:
            # Try with semicolon delimiter
            feedback.pushInfo("  Trying semicolon delimiter...")
            uri = uri.replace("delimiter=,", "delimiter=;")
            layer = QgsVectorLayer(uri, layer_name, "delimitedtext")

        if not layer.isValid() or layer.featureCount() == 0:
            # Try with tab delimiter
            feedback.pushInfo("  Trying tab delimiter...")
            uri = uri.replace("delimiter=;", "delimiter=\\t")
            layer = QgsVectorLayer(uri, layer_name, "delimitedtext")

        if not layer.isValid():
            raise ValueError(
                f"Cannot load CSV file: {csv_path}\n"
                f"Please check the file exists and has valid CSV format."
            )

        if layer.featureCount() == 0:
            raise ValueError(f"CSV file appears to be empty: {csv_path}")

        # Log field information for debugging
        field_names = [field.name() for field in layer.fields()]
        feedback.pushInfo(f"  ✓ Fields detected: {', '.join(field_names)}")
        feedback.pushInfo(f"  ✓ Record count: {layer.featureCount()}")

        # Track for cleanup
        self.temp_layers.append(layer)

        return layer

    def _clean_numeric_data(self, input_layer, feedback):
        """Clean numeric data by removing formatting and converting dashes to NULL (from v5)"""
        feedback.pushInfo("  Cleaning numeric data format...")

        # Get list of all fields to clean
        if isinstance(input_layer, str):
            layer = QgsProcessingUtils.mapLayerFromString(input_layer, self.context)
        else:
            layer = input_layer

        fields_mapping = []
        for field in layer.fields():
            field_name = field.name()

            # Identify numeric fields that need cleaning
            numeric_area_fields = [
                "Grundareal",
                "Registreret areal",
                "Vejareal",
                "Fredskovsareal",
                "Strandbeskyttelsesareal",
                "Brugsretsareal",
                "Beboelsesareal",
                "Erhvervsareal",
                "Bygningsareal",
                "Enhedsareal - Beboelse",
                "Enhedsareal - Erhverv",
                "Areal af udnyttet del af tagetage",
                "Kælderareal",
                "Tinglyst areal (Ejerlejligheder)",
                "Etageareal (BR18-§455)",
            ]

            price_fields = [
                "Offentlig ejendomsværdi (Ny)",
                "Offentlig grundværdi (Ny)",
                "Seneste handelspris",
                "Pris pr. m2 (BR18-§455 etageareal)",
                "Offentlig ejendomsværdi (Historisk)",
                "Offentlig grundværdi (Historisk)",
                "Offentlig ejendomsværdi (Foreløbig)",
                "Offentlig grundværdi (Foreløbig)",
                "Grundskyld (Historisk)",
                "Dækningsafgift (Historisk)",
                "Ejendomsværdiskat (Historisk)",
                "Totale skat (Historisk)",
                "Grundskyld (Ny)",
                "Ejendomsværdiskat (Ny)",
                "Totale skat (Ny)",
                "Grundskyld (Foreløbig)",
                "Ejendomsværdiskat (Foreløbig)",
                "Totale skat (Foreløbig)",
                "Pris pr. m2 (samlet enhedsareal)",
                "Pris pr. m2 (enhedsareal)",
                "Pris pr. m2 (grund)",
            ]

            percentage_fields = [
                "Realiseret bebyggelsesprocent",
                "Seneste handlet andel",
            ]

            integer_fields = [
                "JordstykkeID",
                "BFE-nummer",
                "Beboelsesenheder",
                "Erhvervsenheder",
                "Opførelsesår",
                "Antal ejere",
                "Primær ejer alder",
                "Vurderingsår (Historisk)",
                "Vurderingsår (Ny)",
                "Vurderingsår (Foreløbig)",
                "Antal værelser",
                "Omtilbygningsår",
                "Fordelingstal (Tæller)",
                "Fordelingstal (Nævner)",
                "Postnr",
                "Primær ejer postnr.",
                "Kommunekode",
                "Ejendomsnummer",
            ]

            # Create appropriate cleaning expression based on field type
            if field_name in numeric_area_fields:
                # Clean area fields with Danish formatting
                expression = f"""CASE
                    WHEN trim("{field_name}") IN ('-', '', 'NULL') OR "{field_name}" IS NULL
                    THEN NULL
                    WHEN length(trim("{field_name}")) = 0
                    THEN NULL
                    ELSE
                        CASE
                            WHEN try(to_real(
                                replace(
                                    replace(
                                        replace(trim("{field_name}"), ' ', ''),
                                        '.', ''
                                    ),
                                    ',', '.'
                                )
                            )) IS NOT NULL
                            THEN to_real(
                                replace(
                                    replace(
                                        replace(trim("{field_name}"), ' ', ''),
                                        '.', ''
                                    ),
                                    ',', '.'
                                )
                            )
                            ELSE NULL
                        END
                END"""
                field_type = 6  # QVariant.Double
                precision = 2

            elif field_name in price_fields:
                # Clean price fields
                expression = f"""CASE
                    WHEN trim("{field_name}") IN ('-', '- kr.', '', 'NULL') OR "{field_name}" IS NULL
                    THEN NULL
                    WHEN length(trim("{field_name}")) = 0
                    THEN NULL
                    ELSE
                        CASE
                            WHEN try(to_real(
                                replace(
                                    replace(
                                        replace(
                                            replace(
                                                replace(
                                                    replace(trim("{field_name}"), ' kr.', ''),
                                                    'kr.', ''
                                                ),
                                                ' ', ''
                                            ),
                                            '.', ''
                                        ),
                                        ',', '.'
                                    ),
                                    ' ', ''
                                )
                            )) IS NOT NULL
                            THEN to_real(
                                replace(
                                    replace(
                                        replace(
                                            replace(
                                                replace(
                                                    replace(trim("{field_name}"), ' kr.', ''),
                                                    'kr.', ''
                                                ),
                                                ' ', ''
                                            ),
                                            '.', ''
                                        ),
                                        ',', '.'
                                    ),
                                    ' ', ''
                                )
                            )
                            ELSE NULL
                        END
                END"""
                field_type = 6  # QVariant.Double
                precision = 2

            elif field_name in percentage_fields:
                # Clean percentage fields
                expression = f"""CASE
                    WHEN trim("{field_name}") IN ('-', '-%', '') OR "{field_name}" IS NULL
                    THEN NULL
                    ELSE to_real(replace(trim("{field_name}"), '%', ''))
                END"""
                field_type = 6  # QVariant.Double
                precision = 2

            elif field_name in integer_fields:
                # Clean integer fields
                expression = f"""CASE
                    WHEN trim("{field_name}") = '-' OR trim("{field_name}") = '' OR "{field_name}" IS NULL
                    THEN NULL
                    WHEN lower(trim("{field_name}")) IN ('true', 'ja', 'yes', 'sand', '1')
                    THEN 1
                    WHEN lower(trim("{field_name}")) IN ('false', 'nej', 'no', 'falsk', '0')
                    THEN 0
                    WHEN length(trim("{field_name}")) > 0
                    THEN to_int(replace(trim("{field_name}"), ' ', ''))
                    ELSE NULL
                END"""
                field_type = 2  # QVariant.Int
                precision = 0

            else:
                # Keep as string, just clean whitespace and handle dashes
                expression = f"""CASE
                    WHEN trim("{field_name}") = '-'
                    THEN NULL
                    ELSE trim("{field_name}")
                END"""
                field_type = 10  # QVariant.String
                precision = 0

            field_map = {
                "alias": "",
                "comment": "",
                "expression": expression,
                "length": field.length(),
                "name": field_name,
                "precision": precision,
                "sub_type": 0,
                "type": field_type,
                "type_name": (
                    "String"
                    if field_type == 10
                    else ("Integer" if field_type == 2 else "Real")
                ),
            }
            fields_mapping.append(field_map)

        params = {
            "INPUT": input_layer,
            "FIELDS_MAPPING": fields_mapping,
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:refactorfields",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result and "OUTPUT" in result:
                self._track_temp_layer(result["OUTPUT"])
                return result["OUTPUT"]

            return None

        except Exception as e:
            feedback.pushInfo(f"  ❌ Data cleaning failed: {str(e)}")
            return None

    def _filter_null_jordstykke_id(self, layer, context, feedback):
        """Filter out records where JordstykkeID IS NULL"""
        temp_path = os.path.join(self.temp_dir, "filtered.gpkg")
        self.temp_files.append(temp_path)

        result = processing.run(
            "native:extractbyattribute",
            {
                "INPUT": layer,
                "FIELD": "JordstykkeID",
                "OPERATOR": 9,  # is not null
                "VALUE": "",
                "OUTPUT": temp_path,
            },
            context=context,
            feedback=feedback,
        )

        return QgsVectorLayer(result["OUTPUT"], "filtered", "ogr")

    def _merge_layers(self, layers, context, feedback):
        """Merge multiple layers into one"""
        if len(layers) == 1:
            return layers[0]

        temp_path = os.path.join(self.temp_dir, "merged.gpkg")
        self.temp_files.append(temp_path)

        result = processing.run(
            "native:mergevectorlayers",
            {"LAYERS": layers, "CRS": layers[0].crs(), "OUTPUT": temp_path},
            context=context,
            feedback=feedback,
        )

        return QgsVectorLayer(result["OUTPUT"], "merged", "ogr")

    def _refactor_fields(self, input_layer, field_mapping, description, feedback):
        """Refactor fields with proper type handling (from v5)"""
        feedback.pushInfo(f"  {description}")

        # Convert field mapping to algorithm format
        fields_mapping = []
        for field_def in field_mapping:
            field_map = {
                "alias": "",
                "comment": "",
                "expression": field_def["expression"],
                "length": field_def["length"],
                "name": field_def["name"],
                "precision": field_def["precision"],
                "sub_type": 0,
                "type": field_def["type"],
                "type_name": (
                    "String"
                    if field_def["type"] == 10
                    else ("Integer" if field_def["type"] == 2 else "Real")
                ),
            }
            fields_mapping.append(field_map)

        params = {
            "INPUT": input_layer,
            "FIELDS_MAPPING": fields_mapping,
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:refactorfields",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result and "OUTPUT" in result:
                self._track_temp_layer(result["OUTPUT"])
                return result["OUTPUT"]

            return None

        except Exception as e:
            feedback.pushInfo(f"  ❌ Refactor failed: {str(e)}")
            return None

    def _convert_text_to_integer(self, input_layer, feedback):
        """Convert text fields to integer with validation (from v5)"""
        feedback.pushInfo("  Converting text fields to integer")

        field_mapping = [
            {
                "expression": '"samletfastejendomlokalid"',
                "length": 36,
                "name": "samletfastejendomlokalid",
                "precision": 0,
                "type": 2,  # QVariant.Int
            },
            {
                "expression": '"lokalid"',
                "length": 36,
                "name": "lokalid",
                "precision": 0,
                "type": 2,  # QVariant.Int
            },
        ]

        return self._refactor_fields(
            input_layer, field_mapping, "Converting to integer", feedback
        )

    def _perform_join(
        self, input1, input2, field1, field2, description, feedback, one_to_many=False
    ):
        """Perform join with statistics logging (from v5)"""
        feedback.pushInfo(f"  {description}")

        params = {
            "INPUT": input1,
            "INPUT_2": input2,
            "FIELD": field1,
            "FIELD_2": field2,
            "FIELDS_TO_COPY": [""],  # Copy all fields
            "METHOD": 0 if one_to_many else 1,  # Join type
            "DISCARD_NONMATCHING": False,  # Keep unmatched
            "PREFIX": "",
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:joinattributestable",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result:
                # Log statistics
                joined = result.get("JOINED_COUNT", 0)
                unjoined = result.get("UNJOINABLE_COUNT", 0)
                feedback.pushInfo(f"    Joined: {joined:,} | Unmatched: {unjoined:,}")

                if "OUTPUT" in result:
                    self._track_temp_layer(result["OUTPUT"])
                    return result["OUTPUT"]

            return None

        except Exception as e:
            feedback.pushInfo(f"  ❌ Join failed: {str(e)}")
            return None

    def _setup_final_fields_v7(self, input_layer, feedback):
        """Setup final output fields with v7 hybrid approach (combining v5 robustness with v6 improvements)"""
        feedback.pushInfo("  Setting up final fields with hybrid v7 approach...")

        # Use the comprehensive field mappings from v6 but with v5's robust structure
        field_mappings = self._get_field_mappings_v6(input_layer)

        params = {
            "INPUT": input_layer,
            "FIELDS_MAPPING": field_mappings,
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:refactorfields",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result and "OUTPUT" in result:
                self._track_temp_layer(result["OUTPUT"])
                return result["OUTPUT"]

            return None

        except Exception as e:
            feedback.pushInfo(f"  ❌ Final field setup failed: {str(e)}")
            return None

    def _get_field_mappings_v6(self, layer):
        """Get field mappings with best practice data types for QGIS"""
        mappings = []

        # Core property identification fields
        mappings.extend(
            [
                {
                    "expression": '"samletfastejendomlokalid"',
                    "length": 50,
                    "name": "PropertyID",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"lokalid"',
                    "length": 50,
                    "name": "LocalID",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"JordstykkeID"',
                    "length": 50,
                    "name": "JordstykkeID",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"BFE-nummer"',
                    "length": 50,
                    "name": "BFE_nummer",
                    "precision": 0,
                    "type": 10,
                },
            ]
        )

        # Location and administrative fields
        mappings.extend(
            [
                {
                    "expression": '"Adresse"',
                    "length": 200,
                    "name": "Adresse",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"Kommunenavn"',
                    "length": 100,
                    "name": "Kommune",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"Kommunekode"',
                    "length": 10,
                    "name": "Kommunekode",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"Matrikelnr"',
                    "length": 50,
                    "name": "Matrikelnr",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"Ejerlavsnavn"',
                    "length": 100,
                    "name": "Ejerlavsnavn",
                    "precision": 0,
                    "type": 10,
                },
            ]
        )

        # Coordinate fields - use proper numeric handling
        mappings.extend(
            [
                {
                    "expression": 'to_real(coalesce("Longitude", 0))',
                    "length": 0,
                    "name": "Longitude",
                    "precision": 8,
                    "type": 6,
                },
                {
                    "expression": 'to_real(coalesce("Latitude", 0))',
                    "length": 0,
                    "name": "Latitude",
                    "precision": 8,
                    "type": 6,
                },
            ]
        )

        # Area fields - convert to proper numeric with best practice handling
        mappings.extend(
            [
                {
                    "expression": 'CASE WHEN "Grundareal" IS NOT NULL THEN to_real(regexp_replace(regexp_replace("Grundareal", "[^0-9,.-]", "", "g"), ",", ".")) ELSE NULL END',
                    "length": 0,
                    "name": "Grundareal_m2",
                    "precision": 2,
                    "type": 6,
                },
                {
                    "expression": 'CASE WHEN "Registreret areal" IS NOT NULL THEN to_real(regexp_replace(regexp_replace("Registreret areal", "[^0-9,.-]", "", "g"), ",", ".")) ELSE NULL END',
                    "length": 0,
                    "name": "Registreret_areal_m2",
                    "precision": 2,
                    "type": 6,
                },
                {
                    "expression": 'CASE WHEN "Beboelsesareal" IS NOT NULL THEN to_real(regexp_replace(regexp_replace("Beboelsesareal", "[^0-9,.-]", "", "g"), ",", ".")) ELSE NULL END',
                    "length": 0,
                    "name": "Beboelsesareal_m2",
                    "precision": 2,
                    "type": 6,
                },
                {
                    "expression": 'CASE WHEN "Erhvervsareal" IS NOT NULL THEN to_real(regexp_replace(regexp_replace("Erhvervsareal", "[^0-9,.-]", "", "g"), ",", ".")) ELSE NULL END',
                    "length": 0,
                    "name": "Erhvervsareal_m2",
                    "precision": 2,
                    "type": 6,
                },
            ]
        )

        # Calculate hectares for easier analysis
        mappings.append(
            {
                "expression": 'CASE WHEN "Grundareal_m2" IS NOT NULL THEN "Grundareal_m2" / 10000.0 ELSE NULL END',
                "length": 0,
                "name": "Grundareal_ha",
                "precision": 4,
                "type": 6,
            }
        )

        # Building information
        mappings.extend(
            [
                {
                    "expression": '"Anvendelse"',
                    "length": 100,
                    "name": "Anvendelse",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": '"Type"',
                    "length": 100,
                    "name": "Type",
                    "precision": 0,
                    "type": 10,
                },
                {
                    "expression": 'to_int(coalesce("Beboelsesenheder", 0))',
                    "length": 0,
                    "name": "Beboelsesenheder",
                    "precision": 0,
                    "type": 2,
                },
                {
                    "expression": 'to_int(coalesce("Erhvervsenheder", 0))',
                    "length": 0,
                    "name": "Erhvervsenheder",
                    "precision": 0,
                    "type": 2,
                },
                {
                    "expression": 'to_int(coalesce("Opførelsesår", 0))',
                    "length": 0,
                    "name": "Opfoerelsesaar",
                    "precision": 0,
                    "type": 2,
                },
            ]
        )

        return mappings

    def _track_temp_layer(self, layer):
        """Track temporary layer for cleanup (from v5)"""
        if layer and layer not in self.temp_layers:
            self.temp_layers.append(layer)

    def _save_final_output(self, layer, output_path, context, feedback):
        """Save final output to GeoPackage"""
        feedback.pushInfo(f"💾 Saving final output to: {output_path}")

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        processing.run(
            "native:package",
            {"LAYERS": [layer], "OUTPUT": output_path, "OVERWRITE": True},
            context=context,
            feedback=feedback,
        )

        feedback.pushInfo(f"✅ Output saved with {layer.featureCount()} features")

    def _cleanup_temp_files(self):
        """Clean up temporary files and directories (enhanced from v5)"""
        # Clean up temp layers first
        for layer in self.temp_layers:
            try:
                if hasattr(layer, "dataProvider"):
                    layer.dataProvider().forceReload()
            except Exception:
                pass  # Ignore cleanup errors for layers

        self.temp_layers.clear()

        # Clean up temp directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                if self.feedback:
                    self.feedback.pushInfo(
                        f"✅ Cleaned up temp directory: {self.temp_dir}"
                    )
            except Exception as e:
                if self.feedback:
                    self.feedback.pushInfo(f"⚠️ Could not clean up temp directory: {e}")
                else:
                    QgsMessageLog.logMessage(
                        f"Warning: Could not clean up temp directory: {e}",
                        "MAT2_v7",
                        Qgis.Warning,
                    )
