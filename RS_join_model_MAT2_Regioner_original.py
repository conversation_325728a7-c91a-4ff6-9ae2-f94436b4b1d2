"""
Model exported as python.
Name : RS_join_model_MAT2
Group : join_model
With QGIS : 34007
"""

from qgis.core import QgsProcessing
from qgis.core import QgsProcessingAlgorithm
from qgis.core import QgsProcessingMultiStepFeedback
from qgis.core import QgsProcessingParameterVectorLayer
from qgis.core import QgsProcessingParameterFeatureSink
from qgis.core import QgsExpression
import processing


class Rs_join_model_mat2(QgsProcessingAlgorithm):

    def initAlgorithm(self, config=None):
        self.addParameter(QgsProcessingParameterVectorLayer('jordstykke_udtrk_senestesagloaklid', '<PERSON><PERSON><PERSON><PERSON><PERSON>_udtræk (senestesagloaklid)', types=[QgsProcessing.TypeVectorPolygon], defaultValue=None))
        self.addParameter(QgsProcessingParameterVectorLayer('stamdata_rs', 'Stamdata_RS', types=[QgsProcessing.TypeVector], defaultValue=None))
        self.addParameter(QgsProcessingParameterVectorLayer('jordstykker_rs', '<PERSON>rdstykker_RS', types=[QgsProcessing.TypeVector], defaultValue=None))
        self.addParameter(QgsProcessingParameterFeatureSink('Lodsejerinformation_rs', 'Lodsejerinformation_RS', type=QgsProcessing.TypeVectorAnyGeometry, createByDefault=True, defaultValue=None))

    def processAlgorithm(self, parameters, context, model_feedback):
        # Use a multi-step feedback, so that individual child algorithm progress reports are adjusted for the
        # overall progress through the model
        feedback = QgsProcessingMultiStepFeedback(7, model_feedback)
        results = {}
        outputs = {}

        # Ændre felter - Stamdata
        alg_params = {
            'FIELDS_MAPPING': [{'alias': None,'comment': None,'expression': '"BFE-nummer"','length': 0,'name': 'BFE-nummer','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"Antal ejere"','length': 0,'name': 'Antal ejere','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"Primær ejer"','length': 0,'name': 'Primær ejer','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Primær ejer alder"','length': 0,'name': 'Primær ejer alder','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Postlinje 1"','length': 0,'name': 'Postlinje 1','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Postnr"','length': 0,'name': 'Postnr','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"By"','length': 0,'name': 'By','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'}],
            'INPUT': parameters['stamdata_rs'],
            'OUTPUT': QgsProcessing.TEMPORARY_OUTPUT
        }
        outputs['NdreFelterStamdata'] = processing.run('native:refactorfields', alg_params, context=context, feedback=feedback, is_child_algorithm=True)

        feedback.setCurrentStep(1)
        if feedback.isCanceled():
            return {}

        # Ændre felter - Jordstykker
        alg_params = {
            'FIELDS_MAPPING': [{'alias': None,'comment': None,'expression': '"JordstykkeID"','length': 0,'name': 'JordstykkeID','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"Matrikelnr"','length': 0,'name': 'Matrikelnr','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Ejerlavsnavn"','length': 0,'name': 'Ejerlavsnavn','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'}],
            'INPUT': parameters['jordstykker_rs'],
            'OUTPUT': QgsProcessing.TEMPORARY_OUTPUT
        }
        outputs['NdreFelterJordstykker'] = processing.run('native:refactorfields', alg_params, context=context, feedback=feedback, is_child_algorithm=True)

        feedback.setCurrentStep(2)
        if feedback.isCanceled():
            return {}

        # Tekst til tal
        alg_params = {
            'FIELDS_MAPPING': [{'alias': None,'comment': None,'expression': '"samletfastejendomlokalid"','length': 36,'name': 'samletfastejendomlokalid','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"lokalid"','length': 36,'name': 'lokalid','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'}],
            'INPUT': parameters['jordstykke_udtrk_senestesagloaklid'],
            'OUTPUT': QgsProcessing.TEMPORARY_OUTPUT
        }
        outputs['TekstTilTal'] = processing.run('native:refactorfields', alg_params, context=context, feedback=feedback, is_child_algorithm=True)

        feedback.setCurrentStep(3)
        if feedback.isCanceled():
            return {}

        # Join udvælgelse & stamdata
        alg_params = {
            'DISCARD_NONMATCHING': False,
            'FIELD': 'samletfastejendomlokalid',
            'FIELDS_TO_COPY': [''],
            'FIELD_2': 'BFE-nummer',
            'INPUT': outputs['TekstTilTal']['OUTPUT'],
            'INPUT_2': outputs['NdreFelterStamdata']['OUTPUT'],
            'METHOD': 1,  # Take attributes of the first matching feature only (one-to-one)
            'PREFIX': None,
            'OUTPUT': QgsProcessing.TEMPORARY_OUTPUT
        }
        outputs['JoinUdvlgelseStamdata'] = processing.run('native:joinattributestable', alg_params, context=context, feedback=feedback, is_child_algorithm=True)

        feedback.setCurrentStep(4)
        if feedback.isCanceled():
            return {}

        # Join midl.join & jordstykker
        alg_params = {
            'DISCARD_NONMATCHING': False,
            'FIELD': 'lokalid',
            'FIELDS_TO_COPY': [''],
            'FIELD_2': 'JordstykkeID',
            'INPUT': outputs['JoinUdvlgelseStamdata']['OUTPUT'],
            'INPUT_2': outputs['NdreFelterJordstykker']['OUTPUT'],
            'METHOD': 0,  # Create separate feature for each matching feature (one-to-many)
            'PREFIX': None,
            'OUTPUT': QgsProcessing.TEMPORARY_OUTPUT
        }
        outputs['JoinMidljoinJordstykker'] = processing.run('native:joinattributestable', alg_params, context=context, feedback=feedback, is_child_algorithm=True)

        feedback.setCurrentStep(5)
        if feedback.isCanceled():
            return {}

        # Endelig opsætning
        alg_params = {
            'FIELDS_MAPPING': [{'alias': None,'comment': None,'expression': '"BFE-nummer"','length': 0,'name': 'BFE-nummer','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"JordstykkeID"','length': 0,'name': 'JordstykkeID','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"Antal ejere"','length': 0,'name': 'Antal ejere','precision': 0,'sub_type': 0,'type': 2,'type_name': 'integer'},{'alias': None,'comment': None,'expression': '"Primær ejer"','length': 0,'name': 'Primær ejer','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Primær ejer alder"','length': 0,'name': 'Primær ejer alder','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Matrikelnr"','length': 0,'name': 'Matrikelnr','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Ejerlavsnavn"','length': 0,'name': 'Ejerlavsnavn','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Postlinje 1"','length': 0,'name': 'Postlinje 1','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"Postnr"','length': 0,'name': 'Postnr','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'},{'alias': None,'comment': None,'expression': '"By"','length': 0,'name': 'By','precision': 0,'sub_type': 0,'type': 10,'type_name': 'text'}],
            'INPUT': outputs['JoinMidljoinJordstykker']['OUTPUT'],
            'OUTPUT': QgsProcessing.TEMPORARY_OUTPUT
        }
        outputs['EndeligOpstning'] = processing.run('native:refactorfields', alg_params, context=context, feedback=feedback, is_child_algorithm=True)

        feedback.setCurrentStep(6)
        if feedback.isCanceled():
            return {}

        # Udtræk ud fra attributter
        alg_params = {
            'FIELD': QgsExpression("'JordstykkeID'").evaluate(),
            'INPUT': outputs['EndeligOpstning']['OUTPUT'],
            'OPERATOR': 9,  # is not null
            'VALUE': None,
            'OUTPUT': parameters['Lodsejerinformation_rs']
        }
        outputs['UdtrkUdFraAttributter'] = processing.run('native:extractbyattribute', alg_params, context=context, feedback=feedback, is_child_algorithm=True)
        results['Lodsejerinformation_rs'] = outputs['UdtrkUdFraAttributter']['OUTPUT']
        return results

    def name(self):
        return 'RS_join_model_MAT2'

    def displayName(self):
        return 'RS_join_model_MAT2'

    def group(self):
        return 'join_model'

    def groupId(self):
        return 'join_model'

    def createInstance(self):
        return Rs_join_model_mat2()
